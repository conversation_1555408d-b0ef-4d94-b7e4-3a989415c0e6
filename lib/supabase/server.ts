import { createClient } from "@supabase/supabase-js"
import { cookies } from "next/headers"
import type { Database } from "./types"

// 创建服务器端版本的 Supabase 客户端（可以读取用户 cookies）
// 注意：这个客户端只能在服务器端组件中使用，并且能够获取用户会话
export const createSupabaseServerClient = async () => {
  const cookieStore = await cookies()

  return createClient<Database>(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        getAll() {
          return cookieStore.getAll()
        },
        setAll(cookiesToSet) {
          try {
            cookiesToSet.forEach(({ name, value, options }) =>
              cookieStore.set(name, value, options)
            )
          } catch {
            // The `setAll` method was called from a Server Component.
            // This can be ignored if you have middleware refreshing
            // user sessions.
          }
        },
      },
    }
  )
}

// 创建服务器端版本的 Supabase 客户端（使用 service role key）
// 注意：这个客户端只能在服务器端组件中使用，用于管理员操作
export const createSupabaseAdminClient = () => {
  if (!process.env.NEXT_PUBLIC_SUPABASE_URL) {
    throw new Error("Missing environment variable: NEXT_PUBLIC_SUPABASE_URL")
  }

  if (!process.env.SUPABASE_SERVICE_ROLE_KEY) {
    throw new Error("缺少环境变量: SUPABASE_SERVICE_ROLE_KEY，请确保 .env.local 文件中包含此变量")
  }

  return createClient<Database>(process.env.NEXT_PUBLIC_SUPABASE_URL, process.env.SUPABASE_SERVICE_ROLE_KEY, {
    auth: {
      persistSession: false,
    },
  })
}
