import { <PERSON>ada<PERSON> } from "next"
import { Suspense } from "react";
import { ProfileHeader } from "@/components/profile/profile-header"
import { ProfileTabs } from "@/components/profile/profile-tabs"
import { createSupabaseServerClient } from "@/lib/supabase/server"
import { redirect } from "next/navigation"

export const metadata: Metadata = {
  title: "Your Profile | Introducing.day",
  description: "View and manage your Introducing.day profile",
}

export default async function ProfilePage() {
  const supabase = await createSupabaseServerClient()

  // Get the current user session from cookies
  const { data: { session }, error: sessionError } = await supabase.auth.getSession()

  // 检查用户认证状态
  const user = session?.user

  // 如果用户未登录，重定向到登录页面
  if (!user) {
    redirect('/signin')
  }

  // 获取用户点赞的产品
  let upvotedProducts = []

  const { data: userVotes, error: votesError } = await supabase
    .from("user_votes")
    .select("product_id, vote_type, created_at")
    .eq("user_id", user.id)
    .eq("vote_type", "upvote")
    .order("created_at", { ascending: false })

  if (votesError) {
    console.error("Error fetching user votes:", votesError)
  }

  // 获取用户点赞的产品详情
  const upvotedProductIds = userVotes?.map(vote => vote.product_id) || []

  if (upvotedProductIds.length > 0) {
    const { data: products, error: productsError } = await supabase
      .from("products")
      .select("*")
      .in("id", upvotedProductIds)
      .order("created_at", { ascending: false })

    if (productsError) {
      console.error("Error fetching products:", productsError)
    } else {
      upvotedProducts = products || []
    }
  }

  return (
    <div className="container py-8 max-w-5xl">
      <ProfileHeader user={user} />
      <Suspense fallback={<div className="text-center p-8">Loading tabs...</div>}>
        <ProfileTabs user={user} upvotedProducts={upvotedProducts} />
      </Suspense>
    </div>
  )
}
