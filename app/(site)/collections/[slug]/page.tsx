import { notFound } from "next/navigation"
import type { Metadata } from "next"
import { BigCard } from "@/components/big-card"
import { getProductsByIds } from "@/lib/supabase/db-server"
import type { Product } from "@/lib/supabase/types"
import { collectionsConfig } from "@/lib/config/site-config"

// 使用集中配置文件中的集合配置
const collections = collectionsConfig

interface CollectionPageProps {
  params: {
    slug: string
  }
}

export async function generateMetadata({ params }: CollectionPageProps): Promise<Metadata> {
  // 在 Next.js 15 中，需要先 await params
  const { slug } = await params
  const collection = collections.find((c) => c.slug === slug)

  if (!collection) {
    return {
      title: "Collection Not Found | Introducing.day",
      description: "The requested collection could not be found.",
    }
  }

  // 获取集合的产品用于图片
  const products = await getProductsByIds(collection.productIds.slice(0, 3))
  const coverImage = products[0]?.coverImageUrl || "/og-image.png"

  return {
    title: `${collection.title} Collection | Introducing.day`,
    description: collection.description || collection.subtitle,
    openGraph: {
      title: `${collection.title} - Introducing.day`,
      description: collection.description || collection.subtitle,
      images: [
        {
          url: coverImage,
          width: 1200,
          height: 630,
          alt: `${collection.title} Collection`,
        },
      ],
    },
    twitter: {
      card: "summary_large_image",
      title: `${collection.title} Collection | Introducing.day`,
      description: collection.description || collection.subtitle,
      images: [coverImage],
    },
  }
}

export default async function CollectionPage({ params }: CollectionPageProps) {
  // 在 Next.js 15 中，需要先 await params
  const { slug } = await params
  const collection = collections.find((c) => c.slug === slug)

  if (!collection) {
    notFound()
  }

  // 从 Supabase 获取此集合的产品
  const products = await getProductsByIds(collection.productIds)

  return (
    <>
      <section className="pt-16 pb-6 md:pt-28 md:pb-10">
        <div className="container px-4 md:px-6">
          <div className="flex flex-col items-center text-center space-y-4">
            <h1 className="text-3xl md:text-5xl font-bold tracking-tighter font-outfit">{collection.title}</h1>
            <p className="text-lg text-muted-foreground max-w-2xl">{collection.description || collection.subtitle}</p>
          </div>
        </div>
      </section>

      <div className="container py-8">
        {products.length > 0 ? (
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-8">
            {products.map((product) => (
              <BigCard key={product.id} product={product} />
            ))}
          </div>
        ) : (
          <div className="text-center py-12">
            <p className="text-muted-foreground">No products found in this collection.</p>
          </div>
        )}
      </div>
    </>
  )
}
