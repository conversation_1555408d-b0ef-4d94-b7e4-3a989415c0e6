import { Suspense } from "react"
import { <PERSON> } from "@/components/hero"
import { Countdown } from "@/components/countdown"
import { getRecentProducts, getProducts, getProductsByIds, getProductsByCategory } from "@/lib/supabase/db-server"
import { ClientHomeContent } from "@/components/client-home-content"
import { ProductsLoading } from "@/components/products-loading"
import { ProductPrefetcher } from "@/components/product-prefetcher"
import { highlightConfig, sidebarContentConfig } from "@/lib/config/site-config"
import { categories } from "@/lib/constants"
import type { Metadata } from "next"

export const metadata: Metadata = {
  title: "Introducing.day - Discover the Latest Product Launches",
  description: "Stay updated with the newest products and tools from innovative creators around the world.",
}

export default async function Home() {
  // 在服务器端获取最近产品数据（这部分数据对首屏渲染很重要）
  const recentProducts = await getRecentProducts(6)
  // 获取所有产品用于预加载
  const allProducts = await getProducts()

  // 在服务器端获取 Highlight 组件所需的所有数据 (for the main Highlight/Winners tab content)
  const highlightFeaturedProducts = await getProductsByIds(highlightConfig.featuredIds)
  const highlightSecondaryProducts = await getProductsByIds(highlightConfig.secondaryIds)
  const highlightTertiaryProducts = await getProductsByIds(highlightConfig.tertiaryIds)

  // 获取 Sidebar 的赞助和特色产品数据
  let sponsoredProductForSidebar = null;
  if (sidebarContentConfig.sponsoredProductId && sidebarContentConfig.sponsoredProductId.length > 0) {
    // Assuming sponsoredProductId in config is an array, take the first one
    // If getProductsByIds returns an array, we'll take the first element.
    const sponsoredProductsArray = await getProductsByIds(sidebarContentConfig.sponsoredProductId);
    if (sponsoredProductsArray && sponsoredProductsArray.length > 0) {
      sponsoredProductForSidebar = sponsoredProductsArray[0];
    }
  }
  const featuredProductsForSidebar = await getProductsByIds(sidebarContentConfig.featuredProductIds);

  // 预加载所有分类的数据
  const categoryProducts: Record<string, any> = {}
  for (const category of categories) {
    if (category !== "All") {
      categoryProducts[category] = await getProductsByCategory(category)
    }
  }

  // 计算明天的日期用于倒计时
  const tomorrow = new Date()
  tomorrow.setDate(tomorrow.getDate() + 1)
  tomorrow.setHours(8, 0, 0, 0)

  return (
    <>
      {/* 添加预加载组件 */}
      <ProductPrefetcher products={allProducts} />

      <Hero recentProducts={recentProducts} />
      <div className="container flex justify-center my-4">
        <Countdown targetDate={tomorrow.toISOString()} />
      </div>
      <section className="py-8">
        <div className="container">
          <ClientHomeContent
            initialProducts={allProducts}
            initialSponsoredProduct={sponsoredProductForSidebar || undefined} // Pass sponsored product for sidebar, converting null to undefined
            initialFeaturedProducts={featuredProductsForSidebar} // Pass featured products for sidebar
            initialSecondaryProducts={highlightSecondaryProducts} // For Highlight/Winners tab
            initialTertiaryProducts={highlightTertiaryProducts}   // For Highlight/Winners tab
            categoryProducts={categoryProducts}
          />
        </div>
      </section>
    </>
  )
}
