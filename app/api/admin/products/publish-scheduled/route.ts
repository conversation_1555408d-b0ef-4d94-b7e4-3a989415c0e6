import { createSupabaseServerClient } from "@/lib/supabase/server"
import { NextResponse } from "next/server"

export async function POST(request: Request) {
  const supabase = await createSupabaseServerClient()

  // Check authentication
  const { data: { session } } = await supabase.auth.getSession()

  if (!session) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
  }

  // Check admin permission
  const { data: admin } = await supabase
    .from("admins")
    .select("*")
    .eq("id", session.user.id)
    .single()

  if (!admin) {
    return NextResponse.json({ error: "Forbidden" }, { status: 403 })
  }

  // Publish all products that are scheduled for today or earlier
  const { data, error } = await supabase
    .from("products")
    .update({
      status: "published",
      published_at: new Date().toISOString()
    })
    .eq("status", "scheduled")
    .lt("scheduled_at", new Date().toISOString())

  if (error) {
    return NextResponse.json({ error: error.message }, { status: 500 })
  }

  return NextResponse.json({
    success: true,
    message: "Scheduled products published successfully"
  })
}
