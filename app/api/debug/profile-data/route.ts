import { NextRequest, NextResponse } from "next/server"
import { createSupabaseServerClient } from "@/lib/supabase/server"

export async function GET(request: NextRequest) {
  try {
    const supabase = await createSupabaseServerClient()

    // Get the current user session
    const { data: { session }, error: sessionError } = await supabase.auth.getSession()

    if (sessionError) {
      return NextResponse.json({ error: "Session error", details: sessionError }, { status: 401 })
    }

    if (!session?.user) {
      return NextResponse.json({
        message: "Not authenticated",
        session: null,
        upvotedProducts: []
      })
    }

    // Get user's upvoted products (same logic as profile page)
    const { data: userVotes, error: votesError } = await supabase
      .from("user_votes")
      .select("product_id, vote_type, created_at")
      .eq("user_id", session.user.id)
      .eq("vote_type", "upvote")
      .order("created_at", { ascending: false })

    const upvotedProductIds = userVotes?.map(vote => vote.product_id) || []

    let upvotedProducts = []
    if (upvotedProductIds.length > 0) {
      const { data: products, error: productsError } = await supabase
        .from("products")
        .select("*")
        .in("id", upvotedProductIds)
        .order("created_at", { ascending: false })

      upvotedProducts = products || []
    }

    return NextResponse.json({
      user: {
        id: session.user.id,
        email: session.user.email,
      },
      userVotes: userVotes,
      upvotedProductIds: upvotedProductIds,
      upvotedProducts: upvotedProducts.map(p => ({
        id: p.id,
        name: p.name,
        tagline: p.tagline
      })),
      errors: {
        votesError,
        productsError: upvotedProductIds.length > 0 ? null : "No products to fetch"
      }
    })

  } catch (error) {
    console.error("Debug API error:", error)
    return NextResponse.json({ error: "Internal server error", details: error }, { status: 500 })
  }
}
