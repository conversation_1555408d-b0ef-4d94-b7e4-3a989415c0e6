"use client"

import { useEffect, useState } from "react"
import { useRouter, usePathname } from "next/navigation"
import { createSupabaseClient } from "@/lib/supabase/client"
import { AppSidebar } from "@/components/app-sidebar"
import { SidebarInset, SidebarProvider } from "@/components/ui/sidebar"

export default function AdminLayout({
  children,
}: {
  children: React.ReactNode
}) {
  const [loading, setLoading] = useState(true)
  const [isAdmin, setIsAdmin] = useState(false)
  const router = useRouter()
  const pathname = usePathname()
  const supabase = createSupabaseClient()

  // Pages that don't require authentication
  const publicPages = ['/admin/login', '/admin/register']
  const isPublicPage = publicPages.includes(pathname)

  useEffect(() => {
    async function checkAdmin() {
      // Skip auth check for public pages
      if (isPublicPage) {
        setLoading(false)
        return
      }

      const { data: { session } } = await supabase.auth.getSession()

      if (!session) {
        router.push('/admin/login')
        return
      }

      // For now, we'll just check if the user is authenticated
      // In production, you should check if the user is in the admins table
      setIsAdmin(true)
      setLoading(false)
    }

    checkAdmin()
  }, [router, supabase, isPublicPage, pathname])

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-gray-900 mx-auto"></div>
          <p className="mt-4 text-lg font-medium">Loading...</p>
        </div>
      </div>
    )
  }

  // Allow access to public pages without authentication
  if (isPublicPage) {
    return <div className="min-h-screen bg-gray-50">{children}</div>
  }

  // Prevent access to admin pages without authentication
  if (!isAdmin) {
    return null
  }

  return (
    <SidebarProvider>
      <AppSidebar variant="inset" />
      <SidebarInset>
        <div className="flex flex-1 flex-col">
          <div className="@container/main flex flex-1 flex-col gap-2">
            <div className="flex flex-col gap-4 py-4 md:gap-6 md:py-6">
              {children}
            </div>
          </div>
        </div>
      </SidebarInset>
    </SidebarProvider>
  )
}
