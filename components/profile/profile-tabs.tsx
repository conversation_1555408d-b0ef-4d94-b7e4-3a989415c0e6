"use client"

import { useState, useEffect } from "react"
import { User } from "@supabase/supabase-js"
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs"
import { Card } from "@/components/ui/card"
import { SmallCard } from "@/components/small-card"
import { mapSupabaseProduct } from "@/lib/supabase/types"
import type { SupabaseProduct } from "@/lib/supabase/types"
import { EmptyState } from "@/components/profile/empty-state"
import { useSearchParams, useRouter } from "next/navigation"

interface ProfileTabsProps {
  user: User | null
  upvotedProducts: SupabaseProduct[]
}

export function ProfileTabs({ user, upvotedProducts }: ProfileTabsProps) {
  const searchParams = useSearchParams()
  const router = useRouter()
  const tabParam = searchParams.get('tab')
  const [activeTab, setActiveTab] = useState(tabParam || "upvoted")

  // Update the URL when the tab changes
  const handleTabChange = (value: string) => {
    setActiveTab(value)
    router.push(`/profile?tab=${value}`, { scroll: false })
  }

  // Update active tab when URL parameter changes
  useEffect(() => {
    if (tabParam && ['upvoted', 'submissions', 'settings'].includes(tabParam)) {
      setActiveTab(tabParam)
    }
  }, [tabParam])

  // Map Supabase products to the application's Product type
  const mappedProducts = upvotedProducts.map(mapSupabaseProduct)

  return (
    <Tabs value={activeTab} className="w-full" onValueChange={handleTabChange}>
      <TabsList className="mb-6">
        <TabsTrigger value="upvoted" className="relative px-4 py-2">
          Upvoted Products
          {mappedProducts.length > 0 && (
            <span className="ml-2 inline-flex items-center justify-center w-5 h-5 text-xs font-medium rounded-full bg-gray-100">
              {mappedProducts.length}
            </span>
          )}
        </TabsTrigger>
        <TabsTrigger value="submissions" className="relative px-4 py-2">
          Your Submissions
        </TabsTrigger>
        <TabsTrigger value="settings" className="relative px-4 py-2">
          Account Details
        </TabsTrigger>
      </TabsList>

      <TabsContent value="upvoted" className="space-y-4">

        {mappedProducts.length > 0 ? (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {mappedProducts.map((product) => (
              <SmallCard key={product.id} product={product} />
            ))}
          </div>
        ) : (
          <EmptyState
            title={user ? "No upvoted products yet" : "Please log in to see your upvoted products"}
            description={user ? "Products you upvote will appear here." : "Log in to track your favorite products."}
            actionText={user ? "Explore Products" : "Sign In"}
            actionLink={user ? "/" : "/signin"}
          />
        )}
      </TabsContent>

      <TabsContent value="submissions" className="space-y-4">
        <EmptyState
          title="No submissions yet"
          description="Products you submit will appear here."
          actionText="Submit a Product"
          actionLink="https://tally.so/r/wLaKdp"
          isExternalLink
        />
      </TabsContent>

      <TabsContent value="settings" className="space-y-4">
        <Card className="p-6">
          <h3 className="text-lg font-medium mb-4">Account Details</h3>
          <p className="text-muted-foreground mb-4">
            View your account information and details.
          </p>
          <div className="space-y-4">
            <div>
              <h4 className="text-sm font-medium mb-1">Email</h4>
              <p className="text-sm text-muted-foreground">{user?.email}</p>
            </div>
            <div>
              <h4 className="text-sm font-medium mb-1">Account Created</h4>
              <p className="text-sm text-muted-foreground">
                {user?.created_at ? new Date(user.created_at).toLocaleDateString() : "N/A"}
              </p>
            </div>
            <div>
              <h4 className="text-sm font-medium mb-1">Last Sign In</h4>
              <p className="text-sm text-muted-foreground">
                {user?.last_sign_in_at ? new Date(user.last_sign_in_at).toLocaleDateString() : "N/A"}
              </p>
            </div>
          </div>
        </Card>
      </TabsContent>
    </Tabs>
  )
}
