"use client"

import { User } from "@supabase/supabase-js"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { But<PERSON> } from "@/components/ui/button"
import { Card } from "@/components/ui/card"
import { LogOut } from "lucide-react"
import { createSupabaseClient } from "@/lib/supabase/client"
import { useRouter } from "next/navigation"
import { useState } from "react"

interface ProfileHeaderProps {
  user: User | null
}

export function ProfileHeader({ user }: ProfileHeaderProps) {
  const router = useRouter()
  const [isLoading, setIsLoading] = useState(false)

  // Get user initials for avatar fallback
  const getInitials = () => {
    if (!user?.email) return "U"
    return user.email.charAt(0).toUpperCase()
  }

  // Handle sign out
  const handleSignOut = async () => {
    try {
      setIsLoading(true)
      const supabase = createSupabaseClient()
      await supabase.auth.signOut()
      router.push("/")
      router.refresh()
    } catch (error) {
      console.error("Error signing out:", error)
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <Card className="p-6 mb-6">
      <div className="flex flex-col md:flex-row items-center md:items-start gap-6">
        <Avatar className="w-24 h-24 border">
          <AvatarImage src={user?.user_metadata?.avatar_url} alt={user?.email || "User"} />
          <AvatarFallback className="text-2xl">{getInitials()}</AvatarFallback>
        </Avatar>

        <div className="flex-1 text-center md:text-left">
          <h1 className="text-2xl font-bold mb-1">
            {user?.user_metadata?.full_name || user?.email?.split("@")[0] || "User"}
          </h1>
          <p className="text-muted-foreground mb-4">{user?.email}</p>

          <div className="flex flex-wrap gap-3 justify-center md:justify-start">
            <Button
              variant="outline"
              size="sm"
              className="gap-2 text-red-500 hover:text-red-600"
              onClick={handleSignOut}
              disabled={isLoading}
            >
              <LogOut size={16} />
              Sign Out
            </Button>
          </div>
        </div>
      </div>
    </Card>
  )
}
